# 华为地图 Vue3 组件

基于华为地图API开发的Vue3地图组件库，提供完整的地图功能封装，包括地图显示、标记管理、信息窗和事件处理等功能。

## 功能特性

- 🗺️ **地图显示**: 支持华为地图的基础显示功能
- 📍 **标记管理**: 完整的标记添加、删除、编辑功能
- 💬 **信息窗**: 支持自定义内容的信息窗显示
- 🎛️ **配置管理**: 可视化的地图配置面板
- 📊 **事件监控**: 实时的地图事件监控和日志
- 🎨 **样式定制**: 支持多种地图样式和主题
- 📱 **响应式设计**: 适配不同屏幕尺寸

## 项目结构

```
src/
├── components/           # Vue组件
│   ├── HuaweiMap.vue    # 主地图组件
│   ├── MapConfigPanel.vue      # 地图配置面板
│   ├── MapEventMonitor.vue     # 事件监控面板
│   ├── MarkerPanel.vue         # 标记管理面板
│   └── InfoWindowPanel.vue     # 信息窗管理面板
├── composables/         # 组合式函数
│   ├── useMapConfig.ts         # 地图配置管理
│   ├── useMapEvents.ts         # 地图事件管理
│   ├── useMapMarkers.ts        # 标记管理
│   └── useInfoWindows.ts       # 信息窗管理
├── config/              # 配置文件
│   └── map-config.ts           # 地图默认配置
├── utils/               # 工具函数
│   └── map-loader.ts           # 地图API加载器
├── types/               # 类型定义
│   └── huawei-map.d.ts         # 华为地图API类型声明
├── examples/            # 示例文件
│   ├── BasicExample.vue        # 基础示例
│   └── AdvancedExample.vue     # 高级示例
└── tests/               # 测试文件
    └── map-component.test.ts   # 组件测试
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 配置API密钥

在项目根目录的 `test.key` 文件中配置您的华为地图API密钥。

### 启动开发服务器

```bash
npm run dev
```

访问 `http://localhost:5173` 查看示例。

## 基础用法

### 简单地图

```vue
<template>
  <HuaweiMap
    :center="{ lat: 39.9042, lng: 116.4074 }"
    :zoom="10"
    width="100%"
    height="400px"
    @map-ready="onMapReady"
  />
</template>

<script setup>
import HuaweiMap from './components/HuaweiMap.vue'

const onMapReady = (map) => {
  console.log('地图加载完成:', map)
}
</script>
```

### 添加标记

```vue
<template>
  <HuaweiMap
    ref="mapRef"
    :center="center"
    :zoom="zoom"
    @map-ready="onMapReady"
  />
</template>

<script setup>
import { ref } from 'vue'
import HuaweiMap from './components/HuaweiMap.vue'

const mapRef = ref()
const center = ref({ lat: 39.9042, lng: 116.4074 })
const zoom = ref(10)

const onMapReady = () => {
  // 添加标记
  mapRef.value.addMarker({
    position: { lat: 39.9042, lng: 116.4074 },
    title: '北京',
    content: '中国首都'
  })
}
</script>
```

## 组件API

### HuaweiMap 组件

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| center | `{lat: number, lng: number}` | `{lat: 23.13, lng: 113.32}` | 地图中心点 |
| zoom | `number` | `8` | 缩放级别 |
| width | `string` | `'100%'` | 地图宽度 |
| height | `string` | `'400px'` | 地图高度 |
| language | `string` | `'zh'` | 地图语言 |
| sourceType | `'vector' \| 'raster'` | `'raster'` | 瓦片类型 |
| mapType | `'ROADMAP' \| 'TERRAIN'` | `'ROADMAP'` | 地图类型 |
| zoomControl | `boolean` | `true` | 是否显示缩放控件 |
| scaleControl | `boolean` | `false` | 是否显示比例尺 |
| enableEventMonitor | `boolean` | `false` | 是否启用事件监控 |

#### Events

| 事件名 | 参数 | 描述 |
|--------|------|------|
| map-ready | `(map)` | 地图加载完成 |
| map-click | `(event)` | 地图点击事件 |
| map-dblclick | `(event)` | 地图双击事件 |
| center-changed | `(center)` | 中心点变化 |
| zoom-changed | `(zoom)` | 缩放级别变化 |
| marker-click | `(marker, event)` | 标记点击事件 |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|--------|------|--------|------|
| addMarker | `(markerData)` | `string` | 添加标记 |
| removeMarker | `(id)` | `boolean` | 删除标记 |
| clearMarkers | `()` | `number` | 清空所有标记 |
| addInfoWindow | `(infoWindowData)` | `string` | 添加信息窗 |
| openInfoWindow | `(id)` | `boolean` | 打开信息窗 |
| closeInfoWindow | `(id)` | `boolean` | 关闭信息窗 |

## 示例

### 基础示例

查看 `src/examples/BasicExample.vue` 了解基础用法。

### 高级示例

查看 `src/examples/AdvancedExample.vue` 了解高级功能，包括：

- 事件监控
- 批量操作
- 预设位置
- 统计信息

## 开发

### 项目设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm run test

# 代码检查
npm run lint
```

### 技术栈

- **Vue 3**: 使用Composition API
- **TypeScript**: 完整的类型支持
- **Vite**: 快速的开发构建工具
- **华为地图API**: 地图服务提供商

## 注意事项

1. **API密钥**: 使用前需要在华为开发者平台申请地图API密钥
2. **域名限制**: 确保您的域名已在华为开发者平台配置
3. **网络环境**: 需要能够访问华为地图API服务
4. **浏览器兼容性**: 支持现代浏览器，建议使用Chrome、Firefox、Safari等

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0

- ✅ 基础地图显示功能
- ✅ 标记管理功能
- ✅ 信息窗功能
- ✅ 事件监控功能
- ✅ 配置管理功能
- ✅ 示例和文档

---

感谢使用华为地图Vue3组件！🎉
