<template>
  <div class="marker-panel">
    <div class="panel-header">
      <h3>标记管理</h3>
      <div class="panel-stats">
        <span>总数: {{ markerCount }}</span>
        <span>可见: {{ visibleMarkerCount }}</span>
      </div>
    </div>

    <div class="panel-actions">
      <button @click="showAddForm = !showAddForm" class="action-btn primary">
        {{ showAddForm ? '取消添加' : '添加标记' }}
      </button>
      <button @click="clearAllMarkers" class="action-btn danger" :disabled="markerCount === 0">
        清空所有
      </button>
      <button @click="fitToView" class="action-btn secondary" :disabled="visibleMarkerCount === 0">
        适应视图
      </button>
    </div>

    <!-- 添加标记表单 -->
    <div v-if="showAddForm" class="add-form">
      <h4>添加新标记</h4>
      <div class="form-group">
        <label>标题:</label>
        <input v-model="newMarker.title" type="text" placeholder="标记标题" />
      </div>
      <div class="form-group">
        <label>描述:</label>
        <textarea v-model="newMarker.content" placeholder="标记描述"></textarea>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label>纬度:</label>
          <input v-model.number="newMarker.position.lat" type="number" step="0.000001" />
        </div>
        <div class="form-group">
          <label>经度:</label>
          <input v-model.number="newMarker.position.lng" type="number" step="0.000001" />
        </div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label>图标URL:</label>
          <input v-model="newMarker.iconUrl" type="text" placeholder="图标地址(可选)" />
        </div>
        <div class="form-group">
          <label>标签文本:</label>
          <input v-model="newMarker.labelText" type="text" placeholder="标签文本(可选)" />
        </div>
      </div>
      <div class="form-options">
        <label class="checkbox-label">
          <input v-model="newMarker.draggable" type="checkbox" />
          <span>可拖拽</span>
        </label>
        <div class="select-group">
          <label>动画:</label>
          <select v-model="newMarker.animation">
            <option value="">无动画</option>
            <option value="DROP">下落</option>
            <option value="BOUNCE">弹跳</option>
          </select>
        </div>
      </div>
      <div class="form-actions">
        <button @click="addNewMarker" class="action-btn primary">添加</button>
        <button @click="addMarkerAtCenter" class="action-btn secondary">在中心添加</button>
      </div>
    </div>

    <!-- 标记列表 -->
    <div class="marker-list">
      <h4>标记列表</h4>
      <div v-if="markers.length === 0" class="empty-state">
        暂无标记
      </div>
      <div v-else class="marker-items">
        <div 
          v-for="marker in markers" 
          :key="marker.id"
          class="marker-item"
          :class="{ 
            selected: selectedMarker?.id === marker.id,
            hidden: marker.visible === false 
          }"
          @click="selectMarker(marker.id)"
        >
          <div class="marker-info">
            <div class="marker-title">
              {{ marker.title || `标记 ${marker.id.slice(-6)}` }}
            </div>
            <div class="marker-position">
              {{ marker.position.lat.toFixed(6) }}, {{ marker.position.lng.toFixed(6) }}
            </div>
            <div v-if="marker.content" class="marker-content">
              {{ marker.content }}
            </div>
          </div>
          <div class="marker-actions">
            <button 
              @click.stop="toggleVisibility(marker.id)" 
              class="icon-btn"
              :title="marker.visible === false ? '显示' : '隐藏'"
            >
              {{ marker.visible === false ? '👁️' : '🙈' }}
            </button>
            <button 
              @click.stop="editMarker(marker)" 
              class="icon-btn"
              title="编辑"
            >
              ✏️
            </button>
            <button 
              @click.stop="removeMarker(marker.id)" 
              class="icon-btn danger"
              title="删除"
            >
              🗑️
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑标记对话框 -->
    <div v-if="editingMarker" class="edit-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h4>编辑标记</h4>
          <button @click="cancelEdit" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>标题:</label>
            <input v-model="editForm.title" type="text" />
          </div>
          <div class="form-group">
            <label>描述:</label>
            <textarea v-model="editForm.content"></textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>纬度:</label>
              <input v-model.number="editForm.position.lat" type="number" step="0.000001" />
            </div>
            <div class="form-group">
              <label>经度:</label>
              <input v-model.number="editForm.position.lng" type="number" step="0.000001" />
            </div>
          </div>
          <div class="form-options">
            <label class="checkbox-label">
              <input v-model="editForm.draggable" type="checkbox" />
              <span>可拖拽</span>
            </label>
            <label class="checkbox-label">
              <input v-model="editForm.visible" type="checkbox" />
              <span>可见</span>
            </label>
          </div>
        </div>
        <div class="modal-actions">
          <button @click="saveEdit" class="action-btn primary">保存</button>
          <button @click="cancelEdit" class="action-btn secondary">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { MarkerData } from '../composables/useMapMarkers';

// Props
interface Props {
  markers: MarkerData[];
  selectedMarker: MarkerData | null;
  markerCount: number;
  visibleMarkerCount: number;
  mapCenter?: { lat: number; lng: number };
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'add-marker', marker: Omit<MarkerData, 'id'>): void;
  (e: 'remove-marker', id: string): void;
  (e: 'update-marker', id: string, updates: Partial<MarkerData>): void;
  (e: 'select-marker', id: string): void;
  (e: 'toggle-visibility', id: string): void;
  (e: 'clear-markers'): void;
  (e: 'fit-to-view'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const showAddForm = ref(false);
const editingMarker = ref<MarkerData | null>(null);

// 新标记表单数据
const newMarker = ref({
  title: '',
  content: '',
  position: { lat: 0, lng: 0 },
  iconUrl: '',
  labelText: '',
  draggable: false,
  animation: '' as '' | 'DROP' | 'BOUNCE'
});

// 编辑表单数据
const editForm = ref<Partial<MarkerData>>({});

// 重置新标记表单
const resetNewMarkerForm = () => {
  newMarker.value = {
    title: '',
    content: '',
    position: { lat: props.mapCenter?.lat || 0, lng: props.mapCenter?.lng || 0 },
    iconUrl: '',
    labelText: '',
    draggable: false,
    animation: ''
  };
};

// 添加新标记
const addNewMarker = () => {
  const markerData: Omit<MarkerData, 'id'> = {
    title: newMarker.value.title || undefined,
    content: newMarker.value.content || undefined,
    position: newMarker.value.position,
    draggable: newMarker.value.draggable,
    animation: newMarker.value.animation || null
  };

  // 添加图标
  if (newMarker.value.iconUrl) {
    markerData.icon = newMarker.value.iconUrl;
  }

  // 添加标签
  if (newMarker.value.labelText) {
    markerData.label = newMarker.value.labelText;
  }

  emit('add-marker', markerData);
  resetNewMarkerForm();
  showAddForm.value = false;
};

// 在地图中心添加标记
const addMarkerAtCenter = () => {
  if (props.mapCenter) {
    newMarker.value.position = { ...props.mapCenter };
    addNewMarker();
  }
};

// 删除标记
const removeMarker = (id: string) => {
  if (confirm('确定要删除这个标记吗？')) {
    emit('remove-marker', id);
  }
};

// 清空所有标记
const clearAllMarkers = () => {
  if (confirm('确定要清空所有标记吗？')) {
    emit('clear-markers');
  }
};

// 切换可见性
const toggleVisibility = (id: string) => {
  emit('toggle-visibility', id);
};

// 选择标记
const selectMarker = (id: string) => {
  emit('select-marker', id);
};

// 适应视图
const fitToView = () => {
  emit('fit-to-view');
};

// 编辑标记
const editMarker = (marker: MarkerData) => {
  editingMarker.value = marker;
  editForm.value = { ...marker };
};

// 保存编辑
const saveEdit = () => {
  if (editingMarker.value && editForm.value) {
    emit('update-marker', editingMarker.value.id, editForm.value);
    cancelEdit();
  }
};

// 取消编辑
const cancelEdit = () => {
  editingMarker.value = null;
  editForm.value = {};
};

// 初始化表单
resetNewMarkerForm();
</script>

<style scoped>
.marker-panel {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.panel-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.panel-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6c757d;
}

.panel-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn.primary {
  background: #007bff;
  color: white;
}

.action-btn.primary:hover {
  background: #0056b3;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #545b62;
}

.action-btn.danger {
  background: #dc3545;
  color: white;
}

.action-btn.danger:hover {
  background: #c82333;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-form {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.add-form h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-options {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
  cursor: pointer;
}

.select-group {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6c757d;
}

.select-group select {
  width: auto;
  min-width: 80px;
}

.form-actions {
  display: flex;
  gap: 8px;
}

.marker-list {
  flex: 1;
  min-height: 0;
}

.marker-list h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  color: #6c757d;
  padding: 20px;
  font-style: italic;
}

.marker-items {
  max-height: 300px;
  overflow-y: auto;
}

.marker-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.marker-item:hover {
  background: #f8f9fa;
}

.marker-item.selected {
  border-color: #007bff;
  background: #e7f3ff;
}

.marker-item.hidden {
  opacity: 0.5;
}

.marker-info {
  flex: 1;
}

.marker-title {
  font-weight: 500;
  color: #495057;
  font-size: 13px;
  margin-bottom: 4px;
}

.marker-position {
  font-size: 11px;
  color: #6c757d;
  font-family: monospace;
  margin-bottom: 4px;
}

.marker-content {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

.marker-actions {
  display: flex;
  gap: 4px;
}

.icon-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: background 0.3s;
}

.icon-btn:hover {
  background: #e9ecef;
}

.icon-btn.danger:hover {
  background: #f8d7da;
}

.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h4 {
  margin: 0;
  color: #495057;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.modal-body {
  padding: 16px;
}

.modal-actions {
  display: flex;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #dee2e6;
  justify-content: flex-end;
}
</style>
