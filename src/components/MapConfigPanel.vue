<template>
  <div class="map-config-panel">
    <div class="config-section">
      <h3>基础配置</h3>

      <div class="config-group">
        <label>地图中心点</label>
        <div class="coordinate-inputs">
          <div class="input-group">
            <label>纬度</label>
            <input
              v-model.number="localConfig.center.lat"
              type="number"
              step="0.000001"
              min="-90"
              max="90"
              class="coordinate-input"
            />
          </div>
          <div class="input-group">
            <label>经度</label>
            <input
              v-model.number="localConfig.center.lng"
              type="number"
              step="0.000001"
              min="-180"
              max="180"
              class="coordinate-input"
            />
          </div>
        </div>
      </div>

      <div class="config-group">
        <label>缩放级别</label>
        <div class="zoom-control">
          <input
            v-model.number="localConfig.zoom"
            type="range"
            :min="localConfig.minZoom"
            :max="localConfig.maxZoom"
            class="zoom-slider"
          />
          <span class="zoom-value">{{ localConfig.zoom }}</span>
        </div>
      </div>

      <div class="config-group">
        <label>地图类型</label>
        <select v-model="localConfig.mapType" class="select-input">
          <option value="ROADMAP">基础地图</option>
          <option value="TERRAIN">地形图</option>
        </select>
      </div>

      <div class="config-group">
        <label>瓦片类型</label>
        <select v-model="localConfig.sourceType" class="select-input">
          <option value="vector">矢量</option>
          <option value="raster">栅格</option>
        </select>
      </div>

      <div class="config-group">
        <label>语言</label>
        <select v-model="localConfig.language" class="select-input">
          <option value="zh">中文</option>
          <option value="en">English</option>
        </select>
      </div>
    </div>

    <div class="config-section">
      <h3>控件配置</h3>

      <div class="controls-grid">
        <label class="control-item">
          <input
            v-model="localConfig.controls.copyright"
            type="checkbox"
          />
          <span>版权信息</span>
        </label>

        <label class="control-item">
          <input
            v-model="localConfig.controls.location"
            type="checkbox"
          />
          <span>定位按钮</span>
        </label>

        <label class="control-item">
          <input
            v-model="localConfig.controls.navigation"
            type="checkbox"
          />
          <span>导航按钮</span>
        </label>

        <label class="control-item">
          <input
            v-model="localConfig.controls.rotate"
            type="checkbox"
          />
          <span>指北针</span>
        </label>

        <label class="control-item">
          <input
            v-model="localConfig.controls.scale"
            type="checkbox"
          />
          <span>比例尺</span>
        </label>

        <label class="control-item">
          <input
            v-model="localConfig.controls.zoom"
            type="checkbox"
          />
          <span>缩放按钮</span>
        </label>

        <label class="control-item">
          <input
            v-model="localConfig.controls.zoomSlider"
            type="checkbox"
          />
          <span>缩放条</span>
        </label>
      </div>
    </div>

    <div class="config-section">
      <h3>样式配置</h3>

      <div class="config-group">
        <label>预设样式</label>
        <select v-model="localConfig.style.presetStyleId" class="select-input">
          <option value="standard">标准</option>
          <option value="night">夜间</option>
          <option value="simple">简洁</option>
        </select>
      </div>

      <div class="config-group">
        <label>Logo位置</label>
        <select v-model="localConfig.style.logoPosition" class="select-input">
          <option value="BOTTOM_LEFT">左下</option>
          <option value="BOTTOM_RIGHT">右下</option>
          <option value="TOP_LEFT">左上</option>
          <option value="TOP_RIGHT">右上</option>
        </select>
      </div>

      <div class="config-group">
        <label>透明度</label>
        <div class="opacity-control">
          <input
            v-model.number="localConfig.style.opacity"
            type="range"
            min="0"
            max="1"
            step="0.1"
            class="opacity-slider"
          />
          <span class="opacity-value">{{ (localConfig.style.opacity * 100).toFixed(0) }}%</span>
        </div>
      </div>
    </div>

    <div class="config-actions">
      <button @click="resetToDefaults" class="action-button secondary">
        重置默认
      </button>
      <button @click="applyConfig" class="action-button primary">
        应用配置
      </button>
    </div>

    <div v-if="validationErrors.length > 0" class="validation-errors">
      <h4>配置错误：</h4>
      <ul>
        <li v-for="error in validationErrors" :key="error">{{ error }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue';
import { useMapConfig, type MapConfigOptions } from '../composables/useMapConfig';

// Props
interface Props {
  modelValue?: MapConfigOptions;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({})
});

// Emits
interface Emits {
  (e: 'update:modelValue', config: MapConfigOptions): void;
  (e: 'config-change', config: MapConfigOptions): void;
}

const emit = defineEmits<Emits>();

// 使用地图配置组合式函数
const { config, updateConfig, resetConfig, validateConfig } = useMapConfig(props.modelValue);

// 本地配置副本（用于实时编辑）
const localConfig = ref({ ...config.value });

// 验证错误
const validationErrors = computed(() => {
  const validation = validateConfig();
  return validation.errors;
});

// 标记是否正在同步，避免循环更新
let isSyncing = false;

// 监听本地配置变化
watch(localConfig, (newConfig) => {
  if (!isSyncing) {
    emit('update:modelValue', newConfig);
    emit('config-change', newConfig);
  }
}, { deep: true });

// 监听外部配置变化
watch(() => props.modelValue, (newConfig) => {
  if (newConfig && !isSyncing) {
    isSyncing = true;
    localConfig.value = { ...newConfig };
    // 使用 nextTick 确保更新完成后再重置标记
    nextTick(() => {
      isSyncing = false;
    });
  }
}, { deep: true });

// 应用配置
const applyConfig = () => {
  const validation = validateConfig();
  if (validation.isValid) {
    updateConfig(localConfig.value);
    emit('config-change', localConfig.value);
  }
};

// 重置为默认配置
const resetToDefaults = () => {
  resetConfig();
  localConfig.value = { ...config.value };
};
</script>

<style scoped>
.map-config-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-height: 600px;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.config-section:last-of-type {
  border-bottom: none;
}

.config-section h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.config-group {
  margin-bottom: 16px;
}

.config-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #34495e;
  font-size: 14px;
}

.coordinate-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.input-group label {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 4px;
}

.coordinate-input,
.select-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.coordinate-input:focus,
.select-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.zoom-control,
.opacity-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.zoom-slider,
.opacity-slider {
  flex: 1;
}

.zoom-value,
.opacity-value {
  min-width: 40px;
  text-align: center;
  font-weight: 500;
  color: #2c3e50;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #34495e;
}

.control-item input[type="checkbox"] {
  margin: 0;
}

.config-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.action-button {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.action-button.primary {
  background-color: #3498db;
  color: white;
}

.action-button.primary:hover {
  background-color: #2980b9;
}

.action-button.secondary {
  background-color: #95a5a6;
  color: white;
}

.action-button.secondary:hover {
  background-color: #7f8c8d;
}

.validation-errors {
  margin-top: 16px;
  padding: 12px;
  background-color: #fdf2f2;
  border: 1px solid #fca5a5;
  border-radius: 4px;
  color: #dc2626;
}

.validation-errors h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.validation-errors ul {
  margin: 0;
  padding-left: 20px;
}

.validation-errors li {
  font-size: 13px;
  margin-bottom: 4px;
}
</style>
