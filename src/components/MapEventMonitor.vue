<template>
  <div class="map-event-monitor">
    <div class="monitor-header">
      <h3>事件监控</h3>
      <div class="monitor-controls">
        <button @click="clearEvents" class="clear-button">
          清空记录
        </button>
        <button @click="toggleAutoScroll" class="toggle-button" :class="{ active: autoScroll }">
          自动滚动
        </button>
      </div>
    </div>

    <div class="current-state">
      <h4>当前状态</h4>
      <div class="state-grid">
        <div class="state-item">
          <label>中心点:</label>
          <span v-if="currentCenter">
            {{ currentCenter.lat.toFixed(6) }}, {{ currentCenter.lng.toFixed(6) }}
          </span>
          <span v-else>-</span>
        </div>
        <div class="state-item">
          <label>缩放级别:</label>
          <span>{{ currentZoom || '-' }}</span>
        </div>
        <div class="state-item">
          <label>方向:</label>
          <span>{{ currentHeading ? currentHeading.toFixed(2) + '°' : '-' }}</span>
        </div>
      </div>
    </div>

    <div class="event-filters">
      <h4>事件过滤</h4>
      <div class="filter-checkboxes">
        <label v-for="eventType in eventTypes" :key="eventType" class="filter-checkbox">
          <input
            v-model="enabledEventTypes"
            :value="eventType"
            type="checkbox"
          />
          <span>{{ eventTypeLabels[eventType] }}</span>
        </label>
      </div>
    </div>

    <div class="event-list-container">
      <h4>事件记录 ({{ filteredEvents.length }})</h4>
      <div ref="eventListRef" class="event-list" :class="{ 'auto-scroll': autoScroll }">
        <div
          v-for="event in filteredEvents"
          :key="event.id"
          class="event-item"
          :class="`event-${event.type}`"
        >
          <div class="event-header">
            <span class="event-type">{{ eventTypeLabels[event.type] || event.type }}</span>
            <span class="event-time">{{ formatTime(event.timestamp) }}</span>
          </div>
          <div v-if="event.data" class="event-data">
            <template v-if="event.data.latLng">
              <span class="data-item">
                坐标: {{ event.data.latLng.lat.toFixed(6) }}, {{ event.data.latLng.lng.toFixed(6) }}
              </span>
            </template>
            <template v-else-if="event.data.lat !== undefined">
              <span class="data-item">
                中心点: {{ event.data.lat.toFixed(6) }}, {{ event.data.lng.toFixed(6) }}
              </span>
            </template>
            <template v-else-if="typeof event.data === 'number'">
              <span class="data-item">
                值: {{ event.data }}
              </span>
            </template>
          </div>
        </div>

        <div v-if="filteredEvents.length === 0" class="no-events">
          暂无事件记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, type PropType } from 'vue';
import type { EventRecord } from '../composables/useMapEvents';

// Props
interface Props {
  eventHistory: EventRecord[];
  currentCenter?: { lat: number; lng: number };
  currentZoom?: number;
  currentHeading?: number;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'clear-events'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const eventListRef = ref<HTMLElement>();
const autoScroll = ref(true);
const enabledEventTypes = ref<string[]>([
  'click', 'dblclick', 'contextmenu', 'centerChanged', 'zoomChanged'
]);

// 事件类型配置
const eventTypes = [
  'click', 'dblclick', 'contextmenu', 'mousedown', 'mouseup',
  'movestart', 'moveend', 'centerChanged', 'zoomChanged', 'headingChanged'
];

const eventTypeLabels: Record<string, string> = {
  click: '单击',
  dblclick: '双击',
  contextmenu: '右键',
  mousedown: '鼠标按下',
  mouseup: '鼠标松开',
  movestart: '开始移动',
  moveend: '结束移动',
  centerChanged: '中心点变化',
  zoomChanged: '缩放变化',
  headingChanged: '方向变化'
};

// 计算属性
const filteredEvents = computed(() => {
  return props.eventHistory.filter(event =>
    enabledEventTypes.value.includes(event.type)
  );
});

// 方法
const clearEvents = () => {
  emit('clear-events');
};

const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value;
};

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  });
};

// 监听事件变化，自动滚动到底部
watch(filteredEvents, async () => {
  if (autoScroll.value && eventListRef.value) {
    await nextTick();
    eventListRef.value.scrollTop = 0; // 滚动到顶部（最新事件）
  }
});
</script>

<style scoped>
.map-event-monitor {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.monitor-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.monitor-controls {
  display: flex;
  gap: 8px;
}

.clear-button,
.toggle-button {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.clear-button:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.toggle-button:hover {
  background: #e9ecef;
}

.toggle-button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.current-state {
  margin-bottom: 16px;
}

.current-state h4 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 14px;
}

.state-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 6px;
}

.state-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
}

.state-item label {
  font-weight: 500;
  color: #6c757d;
}

.state-item span {
  color: #495057;
  font-family: monospace;
}

.event-filters {
  margin-bottom: 16px;
}

.event-filters h4 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 14px;
}

.filter-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

.filter-checkbox {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
  cursor: pointer;
}

.filter-checkbox input {
  margin: 0;
}

.event-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.event-list-container h4 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 14px;
}

.event-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
  min-height: 200px;
  max-height: 400px;
}

.event-item {
  margin-bottom: 6px;
  padding: 10px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #dee2e6;
  font-size: 13px;
  line-height: 1.4;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.event-item.event-click {
  border-left-color: #28a745;
}

.event-item.event-dblclick {
  border-left-color: #17a2b8;
}

.event-item.event-contextmenu {
  border-left-color: #ffc107;
}

.event-item.event-centerChanged {
  border-left-color: #6f42c1;
}

.event-item.event-zoomChanged {
  border-left-color: #fd7e14;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.event-type {
  font-weight: 500;
  color: #495057;
}

.event-time {
  color: #6c757d;
  font-family: monospace;
  font-size: 11px;
}

.event-data {
  color: #6c757d;
  font-family: monospace;
  font-size: 11px;
}

.data-item {
  display: block;
  margin-bottom: 2px;
}

.no-events {
  text-align: center;
  color: #6c757d;
  padding: 20px;
  font-style: italic;
}
</style>
